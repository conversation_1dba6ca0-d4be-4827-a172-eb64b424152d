{"version": 3, "names": ["_reactNative", "require", "parsePossibleSources", "source", "uri", "sourceName", "sourceJson", "JSON", "stringify", "includes", "sourceDotLottieURI", "sourceURL", "Image", "resolveAssetSource", "undefined"], "sourceRoot": "../../../src", "sources": ["LottieView/utils.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,SAASC,oBAAoBA,CAACC,MAAM,EAOtB;EACZ,MAAMC,GAAG,GAAID,MAAM,CAASC,GAAG;EAE/B,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO;MAAEE,UAAU,EAAEF;IAAO,CAAC;EAC/B;EAEA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,CAACC,GAAG,EAAE;IACtC,OAAO;MAAEE,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACL,MAAM;IAAE,CAAC;EAC/C;EAEA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIC,GAAG,EAAE;IACrC;IACA,IAAIA,GAAG,CAACK,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC3B,OAAO;QAAEC,kBAAkB,EAAEN;MAAI,CAAC;IACpC;IAEA,OAAO;MAAEO,SAAS,EAAEP;IAAI,CAAC;EAC3B;EAEA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO;MAAEO,kBAAkB,EAAEE,kBAAK,CAACC,kBAAkB,CAACV,MAAM,CAAC,CAACC;IAAI,CAAC;EACrE;EAEA,OAAOU,SAAS;AAClB", "ignoreList": []}