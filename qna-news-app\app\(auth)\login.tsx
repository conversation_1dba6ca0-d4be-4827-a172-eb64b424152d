import { Colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { signInWithEmailAndPassword } from 'firebase/auth';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Platform, StatusBar, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import Animated, { FadeInDown, FadeInUp, SlideInDown } from 'react-native-reanimated';
import LanguageToggle from '../../components/LanguageToggle';
import ThemeToggle from '../../components/ThemeToggle';
import { useLanguage } from '../../context/LanguageContext';
import { useTheme } from '../../context/ThemeContext';
import { auth } from '../../firebaseConfig';

const LoginScreen = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [fontsLoaded, setFontsLoaded] = useState(false);
  const { t, isRTL } = useLanguage();
  const { theme } = useTheme();
  const colors = Colors[theme];

  // Simulate loading custom fonts
  useEffect(() => {
    const loadFonts = async () => {
      // In a real app, we'd load custom fonts here using Font.loadAsync
      // For now, we'll just simulate a delay
      setTimeout(() => {
        setFontsLoaded(true);
      }, 500);
    };
    
    loadFonts();
  }, []);

  const handleLogin = async () => {
    if (loading || !email || !password) return;
    setLoading(true);
    try {
      await signInWithEmailAndPassword(auth, email, password);
      // On successful login, the root layout will handle the redirect.
    } catch (error: any) {
      Alert.alert(t('auth.login.failed'), t('auth.login.check'));
    } finally {
      setLoading(false);
    }
  };

  if (!fontsLoaded) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={theme === 'dark' ? 'light-content' : 'dark-content'} />
      
      {/* Gradient Header Background */}
      <LinearGradient
        colors={theme === 'dark' 
          ? ['#8A1538', '#6B0F2C', '#121212'] 
          : ['#8A1538', '#A61E42', '#CC2952']}
        style={styles.gradientHeader}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      <View style={[styles.languageToggleContainer, { alignItems: isRTL ? 'flex-start' : 'flex-end' }]}>
        <View style={styles.toggleRow}>
          <ThemeToggle style={styles.themeToggle} />
          <LanguageToggle showText={true} />
        </View>
      </View>

      {/* Logo */}
      <Animated.View entering={FadeInUp.delay(300).duration(1000)} style={styles.logoContainer}>
        <Text style={styles.logoQ}>Q</Text>
        <Text style={styles.logoNA}>NA</Text>
        <Animated.Text entering={SlideInDown.delay(600).duration(800)} style={styles.logoSubtitle}>
          {t('app.name')}
        </Animated.Text>
      </Animated.View>

      {/* Login Form */}
      <Animated.View 
        entering={FadeInDown.delay(500).duration(1000)} 
        style={[
          styles.loginBox, 
          { 
            backgroundColor: theme === 'dark' ? 'rgba(30, 30, 30, 0.8)' : 'rgba(255, 255, 255, 0.9)',
            borderColor: colors.border 
          }
        ]}
      >
        <Text style={[styles.title, { color: colors.text }]}>{t('auth.title')}</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>{t('auth.subtitle')}</Text>
        
        <View style={styles.inputContainer}>
          <Ionicons name="mail-outline" size={22} color={colors.iconSecondary} style={[styles.inputIcon, isRTL && styles.inputIconRTL]} />
          <TextInput
            style={[
              styles.input, 
              { 
                textAlign: isRTL ? 'right' : 'left',
                borderColor: colors.border,
                color: colors.text,
                backgroundColor: theme === 'dark' ? 'rgba(42, 42, 42, 0.8)' : 'rgba(248, 249, 250, 0.8)'
              }
            ]}
            placeholder={t('auth.email')}
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
            placeholderTextColor={colors.textSecondary}
          />
        </View>

        <View style={styles.inputContainer}>
          <Ionicons name="lock-closed-outline" size={22} color={colors.iconSecondary} style={[styles.inputIcon, isRTL && styles.inputIconRTL]} />
          <TextInput
            style={[
              styles.input, 
              { 
                textAlign: isRTL ? 'right' : 'left',
                borderColor: colors.border,
                color: colors.text,
                backgroundColor: theme === 'dark' ? 'rgba(42, 42, 42, 0.8)' : 'rgba(248, 249, 250, 0.8)'
              }
            ]}
            placeholder={t('auth.password')}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            placeholderTextColor={colors.textSecondary}
          />
        </View>

        <TouchableOpacity 
          style={[styles.button, { backgroundColor: colors.primary }]} 
          onPress={handleLogin} 
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.buttonText}>{t('auth.login')}</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity style={styles.forgotPasswordContainer}>
          <Text style={[styles.forgotPassword, { color: colors.textSecondary }]}>
            {t('auth.forgot.password')}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
    container: { 
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center',
        padding: 20,
    },
    gradientHeader: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '40%',
    },
    languageToggleContainer: {
        position: 'absolute',
        top: 50,
        right: 20,
        left: 20,
        zIndex: 1,
    },
    toggleRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    themeToggle: {
        marginRight: 16,
    },
    logoContainer: {
        alignItems: 'center',
        marginBottom: 40,
    },
    logoQ: {
        fontSize: 72,
        fontWeight: 'bold',
        color: '#FFFFFF',
        fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
        letterSpacing: 2,
        ...(Platform.OS === 'web'
          ? { textShadow: '2px 2px 4px rgba(0, 0, 0, 0.3)' }
          : {
              textShadowColor: 'rgba(0, 0, 0, 0.3)',
              textShadowOffset: { width: 2, height: 2 },
              textShadowRadius: 4,
            }),
    },
    logoNA: {
        fontSize: 48,
        fontWeight: 'bold',
        color: '#FFFFFF',
        fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
        letterSpacing: 2,
        marginTop: -24,
        ...(Platform.OS === 'web'
          ? { textShadow: '1px 1px 3px rgba(0, 0, 0, 0.3)' }
          : {
              textShadowColor: 'rgba(0, 0, 0, 0.3)',
              textShadowOffset: { width: 1, height: 1 },
              textShadowRadius: 3,
            }),
    },
    logoSubtitle: {
        fontSize: 16,
        color: '#FFFFFF',
        fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'sans-serif',
        letterSpacing: 3,
        marginTop: 8,
        ...(Platform.OS === 'web'
          ? { textShadow: '1px 1px 2px rgba(0, 0, 0, 0.3)' }
          : {
              textShadowColor: 'rgba(0, 0, 0, 0.3)',
              textShadowOffset: { width: 1, height: 1 },
              textShadowRadius: 2,
            }),
    },
    loginBox: {
        width: '100%',
        maxWidth: 400,
        padding: 30,
        borderRadius: 8,
        borderWidth: 1,
        ...(Platform.OS === 'web'
          ? { boxShadow: '0 8px 20px rgba(0,0,0,0.2)' }
          : {
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.2,
              shadowRadius: 20,
              elevation: 10,
            }),
    },
    title: { 
        fontSize: 28, 
        fontWeight: 'bold', 
        textAlign: 'center', 
        marginBottom: 8,
        fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif',
    },
    subtitle: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 30,
        fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'sans-serif',
    },
    inputContainer: {
        position: 'relative',
        marginBottom: 16,
    },
    inputIcon: {
        position: 'absolute',
        left: 16,
        top: 13,
        zIndex: 1,
    },
    inputIconRTL: {
        left: undefined,
        right: 16,
    },
    input: { 
        height: 50, 
        borderWidth: 1, 
        borderRadius: 8, 
        paddingHorizontal: 15, 
        paddingLeft: 45,
        fontSize: 16,
        fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'sans-serif',
    },
    button: {
        padding: 16,
        borderRadius: 8,
        alignItems: 'center',
        marginTop: 10,
        ...(Platform.OS === 'web'
          ? { boxShadow: '0 4px 8px rgba(0,0,0,0.2)' }
          : {
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.2,
              shadowRadius: 8,
              elevation: 5,
            }),
    },
    buttonText: { 
        color: '#FFFFFF', 
        fontSize: 16, 
        fontWeight: 'bold',
        fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'sans-serif',
    },
    forgotPasswordContainer: {
        marginTop: 20,
        alignItems: 'center',
    },
    forgotPassword: {
        fontSize: 14,
        textDecorationLine: 'underline',
    }
});

export default LoginScreen;
