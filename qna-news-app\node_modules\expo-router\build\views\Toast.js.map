{"version": 3, "file": "Toast.js", "sourceRoot": "", "sources": ["../../src/views/Toast.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;;AA0Bb,oCASC;AAED,sBAoCC;AAvED,+DAA0E;AAC1E,kDAAsD;AACtD,+CAAoG;AACpG,mFAA8D;AAEjD,QAAA,SAAS,GAAG,uBAAQ,CAAC,MAAM,CAAC;IACvC,OAAO,EAAE,SAAS;IAClB,GAAG,EAAE,aAAa;IAClB,OAAO,EAAE,WAAW;CACrB,CAAC,CAAC;AAEH,SAAS,SAAS;IAChB,sDAAsD;IACtD,MAAM,CAAC,KAAK,CAAC,GAAG,eAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,uBAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,uBAAQ,CAAC,MAAM,CAAC,KAAK,EAAE;YACrB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,GAAG;YACb,eAAe,EAAE,IAAI;SACtB,CAAC,CAAC,KAAK,EAAE,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,YAAY,CAAC,EAAE,QAAQ,EAA2B;IAChE,MAAM,QAAQ,GAAG,eAAK,CAAC,GAAG,CAAC,uCAAyB,CAAC,CAAC;IACtD,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,mBAAI,CAAC,CAAC,CAAC,6CAAY,CAAC;IAE/C,OAAO,CACL,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAC9C;MAAA,CAAC,QAAQ,CACX;IAAA,EAAE,OAAO,CAAC,CACX,CAAC;AACJ,CAAC;AAED,SAAgB,KAAK,CAAC,EACpB,QAAQ,EACR,QAAQ,EACR,OAAO,GAIP;IACA,MAAM,cAAc,GAAG,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACxC,IAAI,CAAC,QAAQ;YAAE,OAAO,SAAS,CAAC;QAChC,OAAO,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC7C,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IACf,MAAM,KAAK,GAAG,SAAS,EAAE,CAAC;IAC1B,OAAO,CACL,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAC5B;MAAA,CAAC,uBAAQ,CAAC,IAAI,CACZ,KAAK,CAAC,CAAC;YACL,MAAM,CAAC,KAAK;YACZ;gBACE,QAAQ,EAAE,uBAAQ,CAAC,MAAM,CAAC;oBACxB,4DAA4D;oBAC5D,GAAG,EAAE,OAAc;oBACnB,OAAO,EAAE,UAAU;iBACpB,CAAC;gBACF,OAAO,EAAE,KAAK;aACf;SACF,CAAC,CACF;QAAA,CAAC,CAAC,OAAO,IAAI,CAAC,gCAAiB,CAAC,KAAK,CAAC,OAAO,EAAG,CAChD;QAAA,CAAC,OAAO,IAAI,CAAC,oBAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAC1F;QAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAC7B;UAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,mBAAI,CAC1C;UAAA,CAAC,cAAc,IAAI,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,mBAAI,CAAC,CAC1E;QAAA,EAAE,mBAAI,CACR;MAAA,EAAE,uBAAQ,CAAC,IAAI,CACjB;IAAA,EAAE,mBAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,SAAS,EAAE;QACT,eAAe,EAAE,aAAa;QAC9B,IAAI,EAAE,CAAC;KACR;IACD,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE;IACtD,KAAK,EAAE;QACL,UAAU,EAAE,QAAQ;QACpB,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,uBAAuB;QACpC,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,CAAC;QACT,IAAI,EAAE,CAAC;QACP,eAAe,EAAE,CAAC;QAClB,iBAAiB,EAAE,EAAE;QACrB,YAAY,EAAE,CAAC;QACf,eAAe,EAAE,OAAO;KACzB;IACD,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;IACtC,QAAQ,EAAE;QACR,UAAU,EAAE,iBAAS;QACrB,OAAO,EAAE,GAAG;QACZ,KAAK,EAAE,OAAO;QACd,QAAQ,EAAE,EAAE;KACb;IACD,IAAI,EAAE,EAAE,UAAU,EAAE,iBAAS,EAAE;CAChC,CAAC,CAAC", "sourcesContent": ["'use client';\n\nimport { BottomTabBarHeightContext } from '@react-navigation/bottom-tabs';\nimport React, { type PropsWithChildren } from 'react';\nimport { ActivityIndicator, Animated, Image, Platform, StyleSheet, Text, View } from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\n\nexport const CODE_FONT = Platform.select({\n  default: 'Courier',\n  ios: 'Courier New',\n  android: 'monospace',\n});\n\nfunction useFadeIn() {\n  // Returns a React Native Animated value for fading in\n  const [value] = React.useState(() => new Animated.Value(0));\n  React.useEffect(() => {\n    Animated.timing(value, {\n      toValue: 1,\n      duration: 200,\n      useNativeDriver: true,\n    }).start();\n  }, []);\n  return value;\n}\n\nexport function ToastWrapper({ children }: React.PropsWithChildren) {\n  const inTabBar = React.use(BottomTabBarHeightContext);\n  const Wrapper = inTabBar ? View : SafeAreaView;\n\n  return (\n    <Wrapper collapsable={false} style={{ flex: 1 }}>\n      {children}\n    </Wrapper>\n  );\n}\n\nexport function Toast({\n  children,\n  filename,\n  warning,\n}: PropsWithChildren<{\n  filename?: string;\n  warning?: boolean;\n}>) {\n  const filenamePretty = React.useMemo(() => {\n    if (!filename) return undefined;\n    return 'app' + filename.replace(/^\\./, '');\n  }, [filename]);\n  const value = useFadeIn();\n  return (\n    <View style={styles.container}>\n      <Animated.View\n        style={[\n          styles.toast,\n          {\n            position: Platform.select({\n              // NOTE(@kitten): This isn't typed to support Web properties\n              web: 'fixed' as any,\n              default: 'absolute',\n            }),\n            opacity: value,\n          },\n        ]}>\n        {!warning && <ActivityIndicator color=\"white\" />}\n        {warning && <Image source={require('expo-router/assets/error.png')} style={styles.icon} />}\n        <View style={{ marginLeft: 8 }}>\n          <Text style={styles.text}>{children}</Text>\n          {filenamePretty && <Text style={styles.filename}>{filenamePretty}</Text>}\n        </View>\n      </Animated.View>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    backgroundColor: 'transparent',\n    flex: 1,\n  },\n  icon: { width: 20, height: 20, resizeMode: 'contain' },\n  toast: {\n    alignItems: 'center',\n    borderWidth: 1,\n    borderColor: 'rgba(255,255,255,0.2)',\n    flexDirection: 'row',\n    bottom: 8,\n    left: 8,\n    paddingVertical: 8,\n    paddingHorizontal: 12,\n    borderRadius: 4,\n    backgroundColor: 'black',\n  },\n  text: { color: 'white', fontSize: 16 },\n  filename: {\n    fontFamily: CODE_FONT,\n    opacity: 0.8,\n    color: 'white',\n    fontSize: 12,\n  },\n  code: { fontFamily: CODE_FONT },\n});\n"]}