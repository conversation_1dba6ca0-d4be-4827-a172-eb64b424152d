{"version": 3, "names": ["React", "forwardRef", "useImperativeHandle", "useEffect", "useCallback", "useState", "parsePossibleSources", "DotLottieReact", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source", "speed", "loop", "webStyle", "autoPlay", "hover", "direction", "progress", "onAnimationLoaded", "onAnimationFailure", "onAnimationFinish", "onAnimationLoop", "ref", "dotLottie", "setDot<PERSON><PERSON><PERSON>", "sources", "dotLottieRefCallback", "addEventListener", "e", "error", "message", "removeEventListener", "undefined", "__DEV__", "console", "warn", "play", "s", "bothDefined", "bothUndefined", "bothEqual", "set<PERSON>rame", "setSegment", "reset", "pause", "resume", "createElement", "data", "sourceJson", "src", "sourceDotLottieURI", "sourceURL", "sourceName", "style", "autoplay", "playOnHover", "mode"], "sourceRoot": "../../../src", "sources": ["LottieView/index.web.tsx"], "mappings": "AAAA,OAAOA,KAAK,IACVC,UAAU,EACVC,mBAAmB,EACnBC,SAAS,EACTC,WAAW,EACXC,QAAQ,QAEH,OAAO;AACd,SAASC,oBAAoB,QAAQ,SAAS;AAE9C,SAAoBC,cAAc,QAAQ,8BAA8B;AAExE,MAAMC,UAAU,gBAAGP,UAAU,CAC3B,CACE;EACEQ,MAAM;EACNC,KAAK;EACLC,IAAI;EACJC,QAAQ;EACRC,QAAQ;EACRC,KAAK;EACLC,SAAS;EACTC,QAAQ;EACRC,iBAAiB;EACjBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACe,CAAC,EAClBC,GAKE,KACC;EACH,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAmB,IAAI,CAAC;EAClE,MAAMmB,OAAO,GAAGlB,oBAAoB,CAACG,MAAM,CAAC;EAC5C,MAAMgB,oBAAoB,GAAGrB,WAAW,CAAEkB,SAAoB,IAAK;IACjEC,YAAY,CAACD,SAAS,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAENnB,SAAS,CAAC,MAAM;IACd,IAAImB,SAAS,EAAE;MACbA,SAAS,CAACI,gBAAgB,CAAC,MAAM,EAAE,MAAM;QACvCT,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAG,CAAC;MACvB,CAAC,CAAC;MACFK,SAAS,CAACI,gBAAgB,CAAC,WAAW,EAAGC,CAAC,IAAK;QAC7CT,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAGS,CAAC,CAACC,KAAK,CAACC,OAAO,CAAC;MACvC,CAAC,CAAC;MACFP,SAAS,CAACI,gBAAgB,CAAC,UAAU,EAAE,MAAM;QAC3CP,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAG,KAAK,CAAC;MAC5B,CAAC,CAAC;MACFG,SAAS,CAACI,gBAAgB,CAAC,MAAM,EAAE,MAAM;QACvCP,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAG,IAAI,CAAC;MAC3B,CAAC,CAAC;MACFG,SAAS,CAACI,gBAAgB,CAAC,OAAO,EAAE,MAAM;QACxCP,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAG,IAAI,CAAC;MAC3B,CAAC,CAAC;MACFG,SAAS,CAACI,gBAAgB,CAAC,MAAM,EAAE,MAAM;QACvCN,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAG,CAAC;MACrB,CAAC,CAAC;MAEF,OAAO,MAAM;QACXE,SAAS,CAACQ,mBAAmB,CAAC,MAAM,CAAC;QACrCR,SAAS,CAACQ,mBAAmB,CAAC,WAAW,CAAC;QAC1CR,SAAS,CAACQ,mBAAmB,CAAC,UAAU,CAAC;QACzCR,SAAS,CAACQ,mBAAmB,CAAC,MAAM,CAAC;QACrCR,SAAS,CAACQ,mBAAmB,CAAC,OAAO,CAAC;QACtCR,SAAS,CAACQ,mBAAmB,CAAC,MAAM,CAAC;MACvC,CAAC;IACH;IACA,OAAOC,SAAS;EAClB,CAAC,EAAE,CACDT,SAAS,EACTJ,kBAAkB,EAClBC,iBAAiB,EACjBF,iBAAiB,EACjBG,eAAe,CAChB,CAAC;EAEFjB,SAAS,CAAC,MAAM;IACd,IAAIa,QAAQ,IAAIe,SAAS,IAAIC,OAAO,EAAE;MACpCC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;IACvE;EACF,CAAC,EAAE,CAAClB,QAAQ,CAAC,CAAC;EAEdd,mBAAmB,CACjBmB,GAAG,EACH,MAAM;IACJ,OAAO;MACLc,IAAI,EAAEA,CAACC,CAAU,EAAET,CAAU,KAAK;QAChC,IAAI,CAACL,SAAS,EAAE;QAChB,IAAI;UACF,MAAMe,WAAW,GAAGD,CAAC,KAAKL,SAAS,IAAIJ,CAAC,KAAKI,SAAS;UACtD,MAAMO,aAAa,GAAGF,CAAC,KAAKL,SAAS,IAAIJ,CAAC,KAAKI,SAAS;UACxD,MAAMQ,SAAS,GAAGZ,CAAC,KAAKS,CAAC;UACzB,IAAIC,WAAW,EAAE;YACf,IAAIE,SAAS,EAAE;cACbjB,SAAS,CAACkB,QAAQ,CAACb,CAAC,CAAC;cACrBL,SAAS,CAACa,IAAI,CAAC,CAAC;cAChB;YACF;YACAb,SAAS,CAACmB,UAAU,CAACL,CAAC,EAAET,CAAC,CAAC;YAC1B;UACF;UACA,IAAIS,CAAC,KAAKL,SAAS,IAAIJ,CAAC,KAAKI,SAAS,EAAE;YACtCT,SAAS,CAACkB,QAAQ,CAACJ,CAAC,CAAC;YACrBd,SAAS,CAACa,IAAI,CAAC,CAAC;UAClB;UACA,IAAIG,aAAa,EAAE;YACjBhB,SAAS,CAACa,IAAI,CAAC,CAAC;UAClB;QACF,CAAC,CAAC,OAAOP,KAAK,EAAE;UACdK,OAAO,CAACL,KAAK,CAACA,KAAK,CAAC;QACtB;MACF,CAAC;MACDc,KAAK,EAAEA,CAAA,KAAM;QACXpB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEkB,QAAQ,CAAC,CAAC,CAAC;MACxB,CAAC;MACDG,KAAK,EAAEA,CAAA,KAAM;QACXrB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEqB,KAAK,CAAC,CAAC;MACpB,CAAC;MACDC,MAAM,EAAEA,CAAA,KAAM;QACZtB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEa,IAAI,CAAC,CAAC;MACnB;IACF,CAAC;EACH,CAAC,EACD,CAACb,SAAS,CACZ,CAAC;EAED,IAAI,CAACE,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,oBACExB,KAAA,CAAA6C,aAAA,CAACtC,cAAc;IACbkB,oBAAoB,EAAEA,oBAAqB;IAC3CqB,IAAI,EAAEtB,OAAO,CAACuB,UAAW;IACzBC,GAAG,EACDxB,OAAO,CAACyB,kBAAkB,IAAIzB,OAAO,CAAC0B,SAAS,IAAI1B,OAAO,CAAC2B,UAC5D;IACDC,KAAK,EAAExC,QAAS;IAChByC,QAAQ,EAAExC,QAAS;IACnBH,KAAK,EAAEA,KAAM;IACbC,IAAI,EAAEA,IAAK;IACX2C,WAAW,EAAExC,KAAM;IACnByC,IAAI,EAAExC,SAAS,KAAK,CAAC,CAAC,GAAG,SAAS,GAAG;EAAU,CAChD,CAAC;AAEN,CACF,CAAC;AAED,SAASP,UAAU", "ignoreList": []}