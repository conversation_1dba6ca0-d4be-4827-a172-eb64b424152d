{"version": 3, "names": ["React", "processColor", "parsePossibleSources", "NativeLottieAnimationView", "Commands", "defaultProps", "source", "undefined", "progress", "speed", "loop", "autoPlay", "enableMergePathsAndroidForKitKatAndAbove", "enableSafeModeAndroid", "cacheComposition", "useNativeLooping", "resizeMode", "colorFilters", "textFiltersAndroid", "textFiltersIOS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PureComponent", "constructor", "props", "play", "bind", "reset", "pause", "resume", "onAnimationFinish", "captureRef", "hover", "__DEV__", "console", "warn", "startFrame", "endFrame", "lottieAnimationViewRef", "evt", "_this$props$onAnimati", "_this$props", "call", "nativeEvent", "isCancelled", "onAnimationFailure", "_this$props$onAnimati2", "_this$props2", "error", "onAnimationLoaded", "_this$props$onAnimati3", "_this$props3", "ref", "render", "_this$props$colorFilt", "style", "duration", "rest", "sources", "sourceJson", "fr", "Math", "round", "op", "map", "colorFilter", "color", "createElement", "_extends"], "sourceRoot": "../../../src", "sources": ["LottieView/index.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAA0CC,YAAY,QAAQ,cAAc;AAE5E,SAASC,oBAAoB,QAAQ,SAAS;AAI9C,OAAOC,yBAAyB,IAC9BC,QAAQ,QACH,6CAA6C;AAIpD,MAAMC,YAAmB,GAAG;EAC1BC,MAAM,EAAEC,SAAS;EACjBC,QAAQ,EAAE,CAAC;EACXC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,KAAK;EACfC,wCAAwC,EAAE,KAAK;EAC/CC,qBAAqB,EAAE,KAAK;EAC5BC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,KAAK;EACvBC,UAAU,EAAE,SAAS;EACrBC,YAAY,EAAE,EAAE;EAChBC,kBAAkB,EAAE,EAAE;EACtBC,cAAc,EAAE;AAClB,CAAC;AAED,OAAO,MAAMC,UAAU,SAASpB,KAAK,CAACqB,aAAa,CAAY;EAC7D,OAAOhB,YAAY,GAAGA,YAAY;EAMlCiB,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACA,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;IAChC,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACD,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACE,KAAK,GAAG,IAAI,CAACA,KAAK,CAACF,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACG,MAAM,GAAG,IAAI,CAACA,MAAM,CAACH,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAACK,UAAU,GAAG,IAAI,CAACA,UAAU,CAACL,IAAI,CAAC,IAAI,CAAC;IAE5C,IAAIF,KAAK,CAACQ,KAAK,IAAIxB,SAAS,IAAIyB,OAAO,EAAE;MACvCC,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;IACrE;EACF;EAEAV,IAAIA,CAACW,UAAmB,EAAEC,QAAiB,EAAQ;IACjDhC,QAAQ,CAACoB,IAAI,CACX,IAAI,CAACa,sBAAsB,EAC3BF,UAAU,IAAI,CAAC,CAAC,EAChBC,QAAQ,IAAI,CAAC,CACf,CAAC;EACH;EAEAV,KAAKA,CAAA,EAAG;IACNtB,QAAQ,CAACsB,KAAK,CAAC,IAAI,CAACW,sBAAsB,CAAC;EAC7C;EAEAV,KAAKA,CAAA,EAAG;IACNvB,QAAQ,CAACuB,KAAK,CAAC,IAAI,CAACU,sBAAsB,CAAC;EAC7C;EAEAT,MAAMA,CAAA,EAAG;IACPxB,QAAQ,CAACwB,MAAM,CAAC,IAAI,CAACS,sBAAsB,CAAC;EAC9C;EAEQR,iBAAiB,GACvBS,GAAmD,IAChD;IAAA,IAAAC,qBAAA,EAAAC,WAAA;IACH,CAAAD,qBAAA,IAAAC,WAAA,OAAI,CAACjB,KAAK,EAACM,iBAAiB,cAAAU,qBAAA,eAA5BA,qBAAA,CAAAE,IAAA,CAAAD,WAAA,EAA+BF,GAAG,CAACI,WAAW,CAACC,WAAW,CAAC;EAC7D,CAAC;EAEOC,kBAAkB,GACxBN,GAA4C,IACzC;IAAA,IAAAO,sBAAA,EAAAC,YAAA;IACH,CAAAD,sBAAA,IAAAC,YAAA,OAAI,CAACvB,KAAK,EAACqB,kBAAkB,cAAAC,sBAAA,eAA7BA,sBAAA,CAAAJ,IAAA,CAAAK,YAAA,EAAgCR,GAAG,CAACI,WAAW,CAACK,KAAK,CAAC;EACxD,CAAC;EAEOC,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA,EAAAC,YAAA;IAChC,CAAAD,sBAAA,IAAAC,YAAA,OAAI,CAAC3B,KAAK,EAACyB,iBAAiB,cAAAC,sBAAA,eAA5BA,sBAAA,CAAAR,IAAA,CAAAS,YAA+B,CAAC;EAClC,CAAC;EAEOpB,UAAUA,CAACqB,GAAuD,EAAE;IAC1E,IAAIA,GAAG,KAAK,IAAI,EAAE;MAChB;IACF;IAEA,IAAI,CAACd,sBAAsB,GAAGc,GAAG;IACjC,IAAI,IAAI,CAAC5B,KAAK,CAACZ,QAAQ,KAAK,IAAI,EAAE;MAChC,IAAI,CAACa,IAAI,CAAC,CAAC;IACb;EACF;EAEA4B,MAAMA,CAAA,EAAoB;IAAA,IAAAC,qBAAA;IACxB,MAAM;MACJC,KAAK;MACLhD,MAAM;MACNK,QAAQ;MACR4C,QAAQ;MACRrC,kBAAkB;MAClBC,cAAc;MACdH,UAAU;MACV,GAAGwC;IACL,CAAC,GAAG,IAAI,CAACjC,KAAK;IAEd,IAAIjB,MAAM,IAAI,IAAI,EAAE;MAClB2B,OAAO,CAACC,IAAI,CAAC,iEAAiE,EAAE5B,MAAM,CAAC;MACvF,OAAO,IAAI;IACb;IAEA,MAAMmD,OAAO,GAAGvD,oBAAoB,CAACI,MAAM,CAAC;IAE5C,MAAMG,KAAK,GACT8C,QAAQ,IAAIE,OAAO,CAACC,UAAU,IAAKpD,MAAM,CAASqD,EAAE,GAChDC,IAAI,CAACC,KAAK,CACPvD,MAAM,CAASwD,EAAE,GAAIxD,MAAM,CAASqD,EAAE,GAAI,IAAI,GAAIJ,QACvD,CAAC,GACC,IAAI,CAAChC,KAAK,CAACd,KAAK;IAEtB,MAAMQ,YAAY,IAAAoC,qBAAA,GAAG,IAAI,CAAC9B,KAAK,CAACN,YAAY,cAAAoC,qBAAA,uBAAvBA,qBAAA,CAAyBU,GAAG,CAAEC,WAAW,KAAM;MAClE,GAAGA,WAAW;MACdC,KAAK,EAAEhE,YAAY,CAAC+D,WAAW,CAACC,KAAK;IACvC,CAAC,CAAC,CAAC;IAEH,oBACEjE,KAAA,CAAAkE,aAAA,CAAC/D,yBAAyB,EAAAgE,QAAA;MACxBhB,GAAG,EAAE,IAAI,CAACrB;IAAW,GACjB0B,IAAI;MACRvC,YAAY,EAAEA,YAAa;MAC3BC,kBAAkB,EAAEA,kBAAmB;MACvCC,cAAc,EAAEA,cAAe;MAC/BV,KAAK,EAAEA,KAAM;MACb6C,KAAK,EAAEA,KAAM;MACbzB,iBAAiB,EAAE,IAAI,CAACA,iBAAkB;MAC1Ce,kBAAkB,EAAE,IAAI,CAACA,kBAAmB;MAC5CI,iBAAiB,EAAE,IAAI,CAACA,iBAAkB;MAC1CrC,QAAQ,EAAEA,QAAS;MACnBK,UAAU,EAAEA;IAAW,GACnByC,OAAO,CACZ,CAAC;EAEN;AACF", "ignoreList": []}