{"version": 3, "file": "useSafeLayoutEffect.js", "sourceRoot": "", "sources": ["../../src/views/useSafeLayoutEffect.ts"], "names": [], "mappings": ";;;AAAA,iCAAwC;AAE3B,QAAA,mBAAmB,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,uBAAe,CAAC,CAAC,CAAC,cAAa,CAAC,CAAC", "sourcesContent": ["import { useLayoutEffect } from 'react';\n\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : function () {};\n"]}