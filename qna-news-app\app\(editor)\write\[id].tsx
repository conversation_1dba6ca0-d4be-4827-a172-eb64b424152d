import { Colors } from '@/constants/Colors';
import { useLanguage } from '@/context/LanguageContext';
import { useTheme } from '@/context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
    Alert,
    Image,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// --- NEW: Import the required modules for export ---
import LottieLoader from '@/components/LottieLoader';
import * as Clipboard from 'expo-clipboard';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

// Get the Gemini API Key from environment variables
const GEMINI_API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;

// Define the types for our AI tools
type AiTool = 'write' | 'summarize' | 'formal' | 'social';

export default function WriteAboutScreen() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const { theme } = useTheme();
  const { t, isRTL, language } = useLanguage();
  const colors = Colors[theme];
  const [writtenText, setWrittenText] = useState('');
  const [htmlContent, setHtmlContent] = useState(''); // For web rich text
  
  // --- NEW: State to handle AI tool loading ---
  const [isAiLoading, setIsAiLoading] = useState(false);
  const [aiToolType, setAiToolType] = useState<AiTool | null>(null);
  
  // --- NEW: State for clipboard feedback ---
  const [copied, setCopied] = useState(false);
  
  const editorRef = useRef<HTMLDivElement>(null);

  // Extract article data from params
  const {
    title = '',
    description = '',
    urlToImage = '',
    publishedAt = '',
    source = '',
    content = '',
  } = params;
  
  // --- NEW: Export Functions ---
  const handleExport = async (format: 'txt' | 'md') => {
    // Determine which content to use: the user's edits or the original article
    const exportTitle = typeof title === 'string' ? title : Array.isArray(title) ? title[0] : 'Untitled Article';
    const exportContent = writtenText || content || description || '';

    if (!exportContent) {
      Alert.alert(t('editor.empty.content.title'), t('editor.empty.content.message'));
      return;
    }

    // Sanitize the title to use as a filename
    const fileName = exportTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const fileUri = `${FileSystem.documentDirectory}${fileName}.${format}`;

    // Combine title and content for the file
    const fileContent = `# ${exportTitle}\n\n${exportContent}`;

    try {
      await FileSystem.writeAsStringAsync(fileUri, fileContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Open the native share sheet
      await Sharing.shareAsync(fileUri, {
        mimeType: format === 'txt' ? 'text/plain' : 'text/markdown',
        dialogTitle: `${t('editor.export.as')} .${format} ${t('editor.file')}`,
      });

    } catch (error) {
      console.error("Failed to export file:", error);
      Alert.alert(t('editor.export.failed.title'), t('editor.export.failed.message'));
    }
  };

  const handleCopyToClipboard = async () => {
    // Determine which content to use, prioritizing the user's edits
    const exportTitle = typeof title === 'string' ? title : Array.isArray(title) ? title[0] : 'Untitled Article';
    const exportContent = writtenText || content || description || '';

    if (!exportContent) {
      Alert.alert(t('editor.empty.content.title'), t('editor.empty.content.message'));
      return;
    }

    const fullText = `# ${exportTitle}\n\n${exportContent}`;
    await Clipboard.setStringAsync(fullText);
    
    // Provide visual feedback
    setCopied(true);
    setTimeout(() => setCopied(false), 2000); // Reset after 2 seconds
  };
  
  // --- NEW: Re-used function to call the Gemini API ---
  const callGeminiAPI = async (prompt: string) => {
    if (!GEMINI_API_KEY) {
      throw new Error("Gemini API Key is missing.");
    }
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ contents: [{ parts: [{ text: prompt }] }] }),
    });
    const data = await response.json();
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      return data.candidates[0].content.parts[0].text;
    }
    throw new Error("Failed to generate content");
  };

  // --- UPDATED: A more robust handler for the AI tools ---
  const handleAiTool = async (tool: AiTool) => {
    setIsAiLoading(true);
    setAiToolType(tool);
    
    let prompt = '';
    const originalArticleInfo = `Title: ${title}\nDescription: ${description}\nContent: ${content || 'No content provided.'}`;

    // Ensure there's text for tools that need it
    if ((tool === 'formal' || tool === 'social') && !writtenText.trim()) {
      alert(t('editor.text.required'));
      setIsAiLoading(false);
      setAiToolType(null);
      return;
    }

    // Get language instruction
    const languageInstruction = language === 'ar' ? 'بالعربية' : 'in English';

    switch (tool) {
      case 'write':
        // Improved prompt for writing an article
        prompt = `You are a professional news journalist. Based *only* on the information in the article provided below, please write a full, well-structured news article ${languageInstruction}. The new article should be objective, formal, and ready for publication by a major news agency.\n\n---START OF ORIGINAL ARTICLE---\n${originalArticleInfo}\n---END OF ORIGINAL ARTICLE---`;
        break;
      case 'summarize':
        prompt = `Provide a concise, neutral summary of the following news article ${languageInstruction}:\n\n---START OF ARTICLE---\n${originalArticleInfo}\n---END OF ARTICLE---`;
        break;
      case 'formal':
        // Improved prompt for making text formal
        prompt = `Please revise the following text to be in a formal, professional style suitable for a news report ${languageInstruction}. The core meaning and all factual information must remain exactly the same. Do not add any new information or opinions.\n\n---START OF TEXT---\n${writtenText}\n---END OF TEXT---`;
        break;
      case 'social':
        prompt = `Convert the following text into an engaging social media post ${languageInstruction}. Add relevant emojis and include 3-5 relevant hashtags at the end.\n\n---START OF TEXT---\n${writtenText}\n---END OF TEXT---`;
        break;
    }

    try {
      const result = await callGeminiAPI(prompt);
      setWrittenText(result); 
      if (Platform.OS === 'web' && editorRef.current) {
        editorRef.current.innerHTML = result.replace(/\n/g, '<br />');
        setHtmlContent(editorRef.current.innerHTML);
      }
    } catch (error) {
      console.error("AI Tool Error Details:", error);
      Alert.alert(t('editor.ai.error.title'), t('editor.ai.error.message'));
    } finally {
      setIsAiLoading(false);
      setAiToolType(null);
    }
  };


  // Toolbar actions for web
  const handleCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      setHtmlContent(editorRef.current.innerHTML);
    }
  };

  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    setHtmlContent(e.currentTarget.innerHTML);
  };

  // Get AI tool message based on the tool type
  const getAiToolMessage = () => {
    switch (aiToolType) {
      case 'write':
        return t('editor.ai.writing');
      case 'summarize':
        return t('editor.ai.summarizing');
      case 'formal':
        return t('editor.ai.formalizing');
      case 'social':
        return t('editor.ai.socializing');
      default:
        return t('editor.ai.processing');
    }
  };

  return (
    <>
      {isAiLoading && (
        <LottieLoader 
          visible={true}
          message={getAiToolMessage()}
          animationType="star-loader"
        />
      )}
      
      <ScrollView contentContainerStyle={[styles.container, { backgroundColor: colors.background }]}>
        {/* Back Button */}
        <TouchableOpacity 
          style={[styles.backButton, { backgroundColor: colors.surface, borderColor: colors.border, flexDirection: isRTL ? 'row-reverse' : 'row' }]} 
          onPress={() => router.back()}
        >
          <Ionicons name={isRTL ? "arrow-forward" : "arrow-back"} size={24} color={colors.text} />
          <Text style={[styles.backButtonText, { color: colors.text, marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
            {t('editor.back.to.home')}
          </Text>
        </TouchableOpacity>

        {/* Header */}
        <Text style={[styles.header, { color: colors.primary, textAlign: isRTL ? 'right' : 'center' }]}>
          {t('editor.title')}
        </Text>
        
        {/* News Card */}
        <View style={[styles.card, { backgroundColor: colors.surface, shadowColor: colors.shadow }]}>
          {urlToImage ? <Image source={{ uri: urlToImage as string }} style={styles.image} /> : null}
          <Text style={[styles.title, { color: colors.primary }]}>{title}</Text>
          <Text style={[styles.meta, { color: colors.textSecondary }]}>{source} | {publishedAt ? new Date(publishedAt as string).toLocaleDateString() : ''}</Text>
          <Text style={[styles.description, { color: colors.text }]}>{description}</Text>
          {content ? <Text style={[styles.content, { color: colors.textSecondary }]}>{content}</Text> : null}
        </View>
        
        {/* --- NEW: AI Tools Section --- */}
        <View style={[styles.aiToolsContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.subheader, { color: colors.text, textAlign: isRTL ? 'right' : 'left' }]}>
            {t('editor.ai.tools')}
          </Text>
          <View style={styles.aiButtonsGrid}>
            <TouchableOpacity 
              style={[styles.aiButton, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border, flexDirection: isRTL ? 'row-reverse' : 'row' }]} 
              onPress={() => handleAiTool('write')}
              disabled={isAiLoading}
            >
              <Ionicons name="pencil-outline" size={20} color={isAiLoading ? colors.iconSecondary : colors.primary} />
              <Text style={[styles.aiButtonText, { color: colors.text, marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
                {t('editor.write.about')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.aiButton, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border, flexDirection: isRTL ? 'row-reverse' : 'row' }]} 
              onPress={() => handleAiTool('summarize')}
              disabled={isAiLoading}
            >
              <Ionicons name="sparkles-outline" size={20} color={isAiLoading ? colors.iconSecondary : colors.primary} />
              <Text style={[styles.aiButtonText, { color: colors.text, marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
                {t('editor.summarize')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.aiButton, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border, flexDirection: isRTL ? 'row-reverse' : 'row' }]} 
              onPress={() => handleAiTool('formal')} 
              disabled={!writtenText || isAiLoading}
            >
              <Ionicons name="school-outline" size={20} color={!writtenText || isAiLoading ? colors.iconSecondary : colors.primary} />
              <Text style={[styles.aiButtonText, { color: !writtenText || isAiLoading ? colors.iconSecondary : colors.text, marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
                {t('editor.make.formal')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.aiButton, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border, flexDirection: isRTL ? 'row-reverse' : 'row' }]} 
              onPress={() => handleAiTool('social')} 
              disabled={!writtenText || isAiLoading}
            >
              <Ionicons name="share-social-outline" size={20} color={!writtenText || isAiLoading ? colors.iconSecondary : colors.primary} />
              <Text style={[styles.aiButtonText, { color: !writtenText || isAiLoading ? colors.iconSecondary : colors.text, marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
                {t('editor.social.media')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Writing Area */}
        <Text style={[styles.subheader, { color: colors.text, textAlign: isRTL ? 'right' : 'left' }]}>
          {t('editor.your.writeup')}
        </Text>
        {Platform.OS === 'web' ? (
          <>
            <View style={{ flexDirection: 'row', marginBottom: 8, gap: 8 }}>
              <Text style={{ ...toolbarButtonStyle, backgroundColor: colors.backgroundSecondary, borderColor: colors.border, color: colors.text } as any} onPress={() => handleCommand('bold')}>B</Text>
              <Text style={{ ...toolbarButtonStyle, backgroundColor: colors.backgroundSecondary, borderColor: colors.border, color: colors.text } as any} onPress={() => handleCommand('italic')}>I</Text>
              <Text style={{ ...toolbarButtonStyle, backgroundColor: colors.backgroundSecondary, borderColor: colors.border, color: colors.text } as any} onPress={() => handleCommand('underline')}>U</Text>
              <Text style={{ ...toolbarButtonStyle, backgroundColor: colors.backgroundSecondary, borderColor: colors.border, color: colors.text } as any} onPress={() => handleCommand('insertUnorderedList')}>• List</Text>
              <Text style={{ ...toolbarButtonStyle, backgroundColor: colors.backgroundSecondary, borderColor: colors.border, color: colors.text } as any} onPress={() => handleCommand('formatBlock', 'blockquote')}>❝</Text>
              <Text style={{ ...toolbarButtonStyle, backgroundColor: colors.backgroundSecondary, borderColor: colors.border, color: colors.text } as any} onPress={() => handleCommand('removeFormat')}>Tx</Text>
            </View>
            <div
              ref={editorRef}
              contentEditable
              suppressContentEditableWarning
              style={{ minHeight: 120, borderRadius: 8, borderWidth: 1, borderColor: colors.border, borderStyle: 'solid', padding: 14, fontSize: 16, backgroundColor: colors.surface, color: colors.text, marginBottom: 18, outline: 'none', fontFamily: 'inherit' } as React.CSSProperties}
              onInput={handleInput}
            />
          </>
        ) : (
          <TextInput
            style={[styles.input, { backgroundColor: colors.surface, borderColor: colors.border, color: colors.text, textAlign: isRTL ? 'right' : 'left' }]}
            placeholder={t('editor.placeholder')}
            placeholderTextColor={colors.textSecondary}
            value={writtenText}
            onChangeText={setWrittenText}
            multiline
            numberOfLines={12}
            textAlignVertical="top"
            editable={!isAiLoading}
          />
        )}
        
        {/* --- NEW: Export Options --- */}
        <View style={[styles.exportContainer, { borderColor: colors.border, backgroundColor: colors.surface }]}>
          <Text style={[styles.exportTitle, { color: colors.textSecondary }]}>
            {t('editor.export.options')}
          </Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity 
              style={[styles.exportButton, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]} 
              onPress={() => handleExport('txt')}
              disabled={isAiLoading}
            >
              <Ionicons name="document-text-outline" size={20} color={isAiLoading ? colors.iconSecondary : colors.primary} />
              <Text style={[styles.buttonText, { color: isAiLoading ? colors.iconSecondary : colors.primary }]}>.txt</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.exportButton, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]} 
              onPress={() => handleExport('md')}
              disabled={isAiLoading}
            >
              <Ionicons name="logo-markdown" size={20} color={isAiLoading ? colors.iconSecondary : colors.primary} />
              <Text style={[styles.buttonText, { color: isAiLoading ? colors.iconSecondary : colors.primary }]}>.md</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.exportButton, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]} 
              onPress={handleCopyToClipboard}
              disabled={isAiLoading}
            >
              <Ionicons name="clipboard-outline" size={20} color={copied ? colors.success : isAiLoading ? colors.iconSecondary : colors.primary} />
              <Text style={[styles.buttonText, { color: copied ? colors.success : isAiLoading ? colors.iconSecondary : colors.primary }]}>
                {copied ? t('editor.copied') : t('editor.copy')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: { padding: 20 },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 20,
    alignSelf: 'flex-start',
    borderWidth: 1,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  header: { fontSize: 24, fontWeight: 'bold', marginBottom: 18, textAlign: 'center' },
  card: {
    borderRadius: 14,
    padding: 16,
    marginBottom: 24,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0 2px 6px rgba(0,0,0,0.08)' }
      : {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.08,
          shadowRadius: 6,
          elevation: 2,
        }),
  },
  image: { width: '100%', height: 180, borderRadius: 10, marginBottom: 12, backgroundColor: '#EEE' },
  title: { fontSize: 20, fontWeight: 'bold', marginBottom: 6 },
  meta: { fontSize: 13, marginBottom: 8 },
  description: { fontSize: 15, marginBottom: 8 },
  content: { fontSize: 14, marginBottom: 8 },
  subheader: { fontSize: 18, fontWeight: '600', marginBottom: 12, marginTop: 8 },
  input: { borderRadius: 8, borderWidth: 1, padding: 14, fontSize: 16, minHeight: 200, marginBottom: 18 },
  // --- NEW STYLES for AI Tools ---
  aiToolsContainer: {
    borderRadius: 14,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
  },
  aiButtonsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  aiButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    width: '48%',
    marginBottom: 10,
    justifyContent: 'center',
  },
  aiButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  aiButtonTextDisabled: {
    color: '#ccc',
  },
  // --- NEW: Export styles ---
  exportContainer: {
    marginTop: 20,
    padding: 16,
    borderTopWidth: 1,
    borderRadius: 8,
    marginBottom: 24,
  },
  exportTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0 1px 3px rgba(0,0,0,0.08)' }
      : {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          elevation: 2,
        }),
  },
  buttonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
});

const toolbarButtonStyle = {
  fontWeight: 'bold' as 'bold',
  fontSize: 16,
  padding: 6,
  borderRadius: 4,
  borderWidth: 1,
  borderStyle: 'solid',
  marginRight: 6,
  cursor: 'pointer',
  userSelect: 'none',
};