{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_utils", "_LottieAnimationViewNativeComponent", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "defaultProps", "source", "undefined", "progress", "speed", "loop", "autoPlay", "enableMergePathsAndroidForKitKatAndAbove", "enableSafeModeAndroid", "cacheComposition", "useNativeLooping", "resizeMode", "colorFilters", "textFiltersAndroid", "textFiltersIOS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "PureComponent", "constructor", "props", "play", "reset", "pause", "resume", "onAnimationFinish", "captureRef", "hover", "__DEV__", "console", "warn", "startFrame", "endFrame", "Commands", "lottieAnimationViewRef", "evt", "_this$props$onAnimati", "_this$props", "nativeEvent", "isCancelled", "onAnimationFailure", "_this$props$onAnimati2", "_this$props2", "error", "onAnimationLoaded", "_this$props$onAnimati3", "_this$props3", "ref", "render", "_this$props$colorFilt", "style", "duration", "rest", "sources", "parsePossibleSources", "sourceJson", "fr", "Math", "round", "op", "map", "colorFilter", "color", "processColor", "createElement", "exports"], "sourceRoot": "../../../src", "sources": ["LottieView/index.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AAIA,IAAAG,mCAAA,GAAAC,uBAAA,CAAAJ,OAAA;AAEqD,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAf,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAAA,SAAAmB,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAR,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAE,CAAA,IAAAC,CAAA,OAAAY,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAD,CAAA,MAAAM,CAAA,CAAAN,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAM,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAIrD,MAAMG,YAAmB,GAAG;EAC1BC,MAAM,EAAEC,SAAS;EACjBC,QAAQ,EAAE,CAAC;EACXC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,KAAK;EACfC,wCAAwC,EAAE,KAAK;EAC/CC,qBAAqB,EAAE,KAAK;EAC5BC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,KAAK;EACvBC,UAAU,EAAE,SAAS;EACrBC,YAAY,EAAE,EAAE;EAChBC,kBAAkB,EAAE,EAAE;EACtBC,cAAc,EAAE;AAClB,CAAC;AAEM,MAAMC,UAAU,SAASC,cAAK,CAACC,aAAa,CAAY;EAC7D,OAAOjB,YAAY,GAAGA,YAAY;EAMlCkB,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACA,IAAI,CAACxB,IAAI,CAAC,IAAI,CAAC;IAChC,IAAI,CAACyB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACzB,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAAC0B,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC1B,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAAC2B,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC3B,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAAC4B,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC5B,IAAI,CAAC,IAAI,CAAC;IAC1D,IAAI,CAAC6B,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC7B,IAAI,CAAC,IAAI,CAAC;IAE5C,IAAIuB,KAAK,CAACO,KAAK,IAAIxB,SAAS,IAAIyB,OAAO,EAAE;MACvCC,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;IACrE;EACF;EAEAT,IAAIA,CAACU,UAAmB,EAAEC,QAAiB,EAAQ;IACjDC,4CAAQ,CAACZ,IAAI,CACX,IAAI,CAACa,sBAAsB,EAC3BH,UAAU,IAAI,CAAC,CAAC,EAChBC,QAAQ,IAAI,CAAC,CACf,CAAC;EACH;EAEAV,KAAKA,CAAA,EAAG;IACNW,4CAAQ,CAACX,KAAK,CAAC,IAAI,CAACY,sBAAsB,CAAC;EAC7C;EAEAX,KAAKA,CAAA,EAAG;IACNU,4CAAQ,CAACV,KAAK,CAAC,IAAI,CAACW,sBAAsB,CAAC;EAC7C;EAEAV,MAAMA,CAAA,EAAG;IACPS,4CAAQ,CAACT,MAAM,CAAC,IAAI,CAACU,sBAAsB,CAAC;EAC9C;EAEQT,iBAAiB,GACvBU,GAAmD,IAChD;IAAA,IAAAC,qBAAA,EAAAC,WAAA;IACH,CAAAD,qBAAA,IAAAC,WAAA,OAAI,CAACjB,KAAK,EAACK,iBAAiB,cAAAW,qBAAA,eAA5BA,qBAAA,CAAA5C,IAAA,CAAA6C,WAAA,EAA+BF,GAAG,CAACG,WAAW,CAACC,WAAW,CAAC;EAC7D,CAAC;EAEOC,kBAAkB,GACxBL,GAA4C,IACzC;IAAA,IAAAM,sBAAA,EAAAC,YAAA;IACH,CAAAD,sBAAA,IAAAC,YAAA,OAAI,CAACtB,KAAK,EAACoB,kBAAkB,cAAAC,sBAAA,eAA7BA,sBAAA,CAAAjD,IAAA,CAAAkD,YAAA,EAAgCP,GAAG,CAACG,WAAW,CAACK,KAAK,CAAC;EACxD,CAAC;EAEOC,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA,EAAAC,YAAA;IAChC,CAAAD,sBAAA,IAAAC,YAAA,OAAI,CAAC1B,KAAK,EAACwB,iBAAiB,cAAAC,sBAAA,eAA5BA,sBAAA,CAAArD,IAAA,CAAAsD,YAA+B,CAAC;EAClC,CAAC;EAEOpB,UAAUA,CAACqB,GAAuD,EAAE;IAC1E,IAAIA,GAAG,KAAK,IAAI,EAAE;MAChB;IACF;IAEA,IAAI,CAACb,sBAAsB,GAAGa,GAAG;IACjC,IAAI,IAAI,CAAC3B,KAAK,CAACb,QAAQ,KAAK,IAAI,EAAE;MAChC,IAAI,CAACc,IAAI,CAAC,CAAC;IACb;EACF;EAEA2B,MAAMA,CAAA,EAAoB;IAAA,IAAAC,qBAAA;IACxB,MAAM;MACJC,KAAK;MACLhD,MAAM;MACNK,QAAQ;MACR4C,QAAQ;MACRrC,kBAAkB;MAClBC,cAAc;MACdH,UAAU;MACV,GAAGwC;IACL,CAAC,GAAG,IAAI,CAAChC,KAAK;IAEd,IAAIlB,MAAM,IAAI,IAAI,EAAE;MAClB2B,OAAO,CAACC,IAAI,CAAC,iEAAiE,EAAE5B,MAAM,CAAC;MACvF,OAAO,IAAI;IACb;IAEA,MAAMmD,OAAO,GAAG,IAAAC,2BAAoB,EAACpD,MAAM,CAAC;IAE5C,MAAMG,KAAK,GACT8C,QAAQ,IAAIE,OAAO,CAACE,UAAU,IAAKrD,MAAM,CAASsD,EAAE,GAChDC,IAAI,CAACC,KAAK,CACPxD,MAAM,CAASyD,EAAE,GAAIzD,MAAM,CAASsD,EAAE,GAAI,IAAI,GAAIL,QACvD,CAAC,GACC,IAAI,CAAC/B,KAAK,CAACf,KAAK;IAEtB,MAAMQ,YAAY,IAAAoC,qBAAA,GAAG,IAAI,CAAC7B,KAAK,CAACP,YAAY,cAAAoC,qBAAA,uBAAvBA,qBAAA,CAAyBW,GAAG,CAAEC,WAAW,KAAM;MAClE,GAAGA,WAAW;MACdC,KAAK,EAAE,IAAAC,yBAAY,EAACF,WAAW,CAACC,KAAK;IACvC,CAAC,CAAC,CAAC;IAEH,oBACE9F,MAAA,CAAAa,OAAA,CAAAmF,aAAA,CAAC3F,mCAAA,CAAAQ,OAAyB,EAAAc,QAAA;MACxBoD,GAAG,EAAE,IAAI,CAACrB;IAAW,GACjB0B,IAAI;MACRvC,YAAY,EAAEA,YAAa;MAC3BC,kBAAkB,EAAEA,kBAAmB;MACvCC,cAAc,EAAEA,cAAe;MAC/BV,KAAK,EAAEA,KAAM;MACb6C,KAAK,EAAEA,KAAM;MACbzB,iBAAiB,EAAE,IAAI,CAACA,iBAAkB;MAC1Ce,kBAAkB,EAAE,IAAI,CAACA,kBAAmB;MAC5CI,iBAAiB,EAAE,IAAI,CAACA,iBAAkB;MAC1CrC,QAAQ,EAAEA,QAAS;MACnBK,UAAU,EAAEA;IAAW,GACnByC,OAAO,CACZ,CAAC;EAEN;AACF;AAACY,OAAA,CAAAjD,UAAA,GAAAA,UAAA", "ignoreList": []}