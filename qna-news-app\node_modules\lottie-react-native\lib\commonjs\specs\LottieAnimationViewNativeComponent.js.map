{"version": 3, "names": ["_codegenNativeComponent", "_interopRequireDefault", "require", "_codegenNativeCommands", "e", "__esModule", "default", "Commands", "exports", "codegenNativeCommands", "supportedCommands", "_default", "codegenNativeComponent"], "sourceRoot": "../../../src", "sources": ["specs/LottieAnimationViewNativeComponent.ts"], "mappings": ";;;;;;AAMA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA2F,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AA6EpF,MAAMG,QAAwB,GAAAC,OAAA,CAAAD,QAAA,GAAG,IAAAE,8BAAqB,EAAiB;EAC5EC,iBAAiB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ;AACxD,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAH,OAAA,CAAAF,OAAA,GAEY,IAAAM,+BAAsB,EAAc,qBAAqB,CAAC", "ignoreList": []}