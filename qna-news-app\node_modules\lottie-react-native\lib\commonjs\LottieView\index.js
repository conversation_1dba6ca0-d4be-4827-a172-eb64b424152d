"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LottieView = void 0;
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _utils = require("./utils");
var _LottieAnimationViewNativeComponent = _interopRequireWildcard(require("../specs/LottieAnimationViewNativeComponent"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }
const defaultProps = {
  source: undefined,
  progress: 0,
  speed: 1,
  loop: true,
  autoPlay: false,
  enableMergePathsAndroidForKitKatAndAbove: false,
  enableSafeModeAndroid: false,
  cacheComposition: true,
  useNativeLooping: false,
  resizeMode: 'contain',
  colorFilters: [],
  textFiltersAndroid: [],
  textFiltersIOS: []
};
class LottieView extends _react.default.PureComponent {
  static defaultProps = defaultProps;
  constructor(props) {
    super(props);
    this.play = this.play.bind(this);
    this.reset = this.reset.bind(this);
    this.pause = this.pause.bind(this);
    this.resume = this.resume.bind(this);
    this.onAnimationFinish = this.onAnimationFinish.bind(this);
    this.captureRef = this.captureRef.bind(this);
    if (props.hover != undefined && __DEV__) {
      console.warn('lottie-react-native: hover is only supported on web');
    }
  }
  play(startFrame, endFrame) {
    _LottieAnimationViewNativeComponent.Commands.play(this.lottieAnimationViewRef, startFrame ?? -1, endFrame ?? -1);
  }
  reset() {
    _LottieAnimationViewNativeComponent.Commands.reset(this.lottieAnimationViewRef);
  }
  pause() {
    _LottieAnimationViewNativeComponent.Commands.pause(this.lottieAnimationViewRef);
  }
  resume() {
    _LottieAnimationViewNativeComponent.Commands.resume(this.lottieAnimationViewRef);
  }
  onAnimationFinish = evt => {
    var _this$props$onAnimati, _this$props;
    (_this$props$onAnimati = (_this$props = this.props).onAnimationFinish) === null || _this$props$onAnimati === void 0 || _this$props$onAnimati.call(_this$props, evt.nativeEvent.isCancelled);
  };
  onAnimationFailure = evt => {
    var _this$props$onAnimati2, _this$props2;
    (_this$props$onAnimati2 = (_this$props2 = this.props).onAnimationFailure) === null || _this$props$onAnimati2 === void 0 || _this$props$onAnimati2.call(_this$props2, evt.nativeEvent.error);
  };
  onAnimationLoaded = () => {
    var _this$props$onAnimati3, _this$props3;
    (_this$props$onAnimati3 = (_this$props3 = this.props).onAnimationLoaded) === null || _this$props$onAnimati3 === void 0 || _this$props$onAnimati3.call(_this$props3);
  };
  captureRef(ref) {
    if (ref === null) {
      return;
    }
    this.lottieAnimationViewRef = ref;
    if (this.props.autoPlay === true) {
      this.play();
    }
  }
  render() {
    var _this$props$colorFilt;
    const {
      style,
      source,
      autoPlay,
      duration,
      textFiltersAndroid,
      textFiltersIOS,
      resizeMode,
      ...rest
    } = this.props;
    if (source == null) {
      console.warn('LottieView needs `source` parameter, provided value for source:', source);
      return null;
    }
    const sources = (0, _utils.parsePossibleSources)(source);
    const speed = duration && sources.sourceJson && source.fr ? Math.round(source.op / source.fr * 1000 / duration) : this.props.speed;
    const colorFilters = (_this$props$colorFilt = this.props.colorFilters) === null || _this$props$colorFilt === void 0 ? void 0 : _this$props$colorFilt.map(colorFilter => ({
      ...colorFilter,
      color: (0, _reactNative.processColor)(colorFilter.color)
    }));
    return /*#__PURE__*/_react.default.createElement(_LottieAnimationViewNativeComponent.default, _extends({
      ref: this.captureRef
    }, rest, {
      colorFilters: colorFilters,
      textFiltersAndroid: textFiltersAndroid,
      textFiltersIOS: textFiltersIOS,
      speed: speed,
      style: style,
      onAnimationFinish: this.onAnimationFinish,
      onAnimationFailure: this.onAnimationFailure,
      onAnimationLoaded: this.onAnimationLoaded,
      autoPlay: autoPlay,
      resizeMode: resizeMode
    }, sources));
  }
}
exports.LottieView = LottieView;
//# sourceMappingURL=index.js.map