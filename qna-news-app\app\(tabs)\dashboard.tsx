import { Colors } from '@/constants/Colors';
import { useLanguage } from '@/context/LanguageContext';
import { useTheme } from '@/context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { collection, doc, onSnapshot } from 'firebase/firestore';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Dimensions, Platform, ScrollView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { <PERSON><PERSON><PERSON>, LineChart, PieChart } from 'react-native-chart-kit';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import TrendChart from '../../components/TrendChart';
import { db } from '../../firebaseConfig';

const screenWidth = Dimensions.get('window').width;

// --- Chart and Map Data Structures ---

// Updated Qatar Maroon-inspired color palette
const pieChartColors = ['#8A1538', '#A61E42', '#CC2952', '#D44D6E', '#E17A93', '#EDA7B7', '#F6D3DB'];
const defaultLineData = { labels: ['...'], datasets: [{ data: [0] }] };
const defaultPieData = [{ name: 'Loading', population: 1, color: '#444444', legendFontColor: '#888888', legendFontSize: 14 }];
const defaultCountryTrends = [
  { countryName: 'Loading', qatarMentionCount: 0 },
];

// =================================================================
// --- MAIN DASHBOARD SCREEN ---
// =================================================================
export default function DashboardScreen() {
    const { theme } = useTheme();
    const { t, isRTL } = useLanguage();
    const colors = Colors[theme];

    const [dailyFlowData, setDailyFlowData] = useState(defaultLineData);
    const [categoryData, setCategoryData] = useState(defaultPieData);
    const [loading, setLoading] = useState(true);
    const [countryTrends, setCountryTrends] = useState(defaultCountryTrends);
    const [countryTrendsLoading, setCountryTrendsLoading] = useState(true);

    const chartConfig = {
        backgroundGradientFrom: colors.surface,
        backgroundGradientTo: colors.surface,
        color: (opacity = 1) => colors.primary,
        labelColor: (opacity = 1) => colors.textSecondary,
        strokeWidth: 2,
        barPercentage: 0.5,
        decimalPlaces: 0,
        useShadowColorFromDataset: false,
        propsForDots: { r: '4', strokeWidth: '2', stroke: colors.primary },
        propsForBackgroundLines: {
            stroke: colors.border,
            strokeDasharray: '6, 6',
        },
        fillShadowGradient: colors.primary,
        fillShadowGradientOpacity: 0.2,
    };

    useEffect(() => {
        let dailyFlowLoaded = false;
        let categoryDistLoaded = false;

        const checkLoadingState = () => {
            if (dailyFlowLoaded && categoryDistLoaded) {
                setLoading(false);
            }
        };

        const translateCategoryName = (key: string) => t(`category.${key.toLowerCase()}`, { defaultValue: key });

        const dailyFlowDocRef = doc(db, 'dashboard_stats', 'daily_flow');
        const unsubscribeDailyFlow = onSnapshot(dailyFlowDocRef, (docSnap) => {
            if (docSnap.exists()) {
                const data = docSnap.data();
                const labels: string[] = [];
                const dataPoints: number[] = [];
                for (let i = 6; i >= 0; i--) {
                    const d = new Date();
                    d.setDate(d.getDate() - i);
                    const dateString = d.toISOString().split('T')[0];
                    const label = isRTL ? `${d.getDate()}/${d.getMonth() + 1}` : `${d.getMonth() + 1}/${d.getDate()}`;
                    labels.push(label);
                    dataPoints.push(data[dateString] || 0);
                }
                setDailyFlowData({ labels, datasets: [{ data: dataPoints }] });
            }
            dailyFlowLoaded = true;
            checkLoadingState();
        });

        const categoryDistDocRef = doc(db, 'dashboard_stats', 'category_distribution');
        const unsubscribeCategoryDist = onSnapshot(categoryDistDocRef, (docSnap) => {
            if (docSnap.exists()) {
                const data = docSnap.data();
                const pieData = Object.keys(data).map((key, index) => ({
                    name: translateCategoryName(key),
                    population: data[key],
                    color: pieChartColors[index % pieChartColors.length],
                    legendFontColor: colors.text,
                    legendFontSize: 14,
                }));
                setCategoryData(pieData.length > 0 ? pieData : defaultPieData);
            }
            categoryDistLoaded = true;
            checkLoadingState();
        });

        // Fetch countryTrends for all platforms
        const fetchCountryTrends = async () => {
            setCountryTrendsLoading(true);
            try {
                // Use onSnapshot to listen for real-time updates
                const countryTrendsRef = collection(db, 'countryTrends');
                const unsubscribeCountryTrends = onSnapshot(countryTrendsRef, (querySnapshot) => {
                    const trendsData: Array<{countryName: string; qatarMentionCount: number}> = [];
                    querySnapshot.forEach((doc) => {
                        const data = doc.data();
                        trendsData.push({
                            countryName: data.countryName || doc.id,
                            qatarMentionCount: data.qatarMentionCount || 0,
                        });
                    });
                    // Sort by count descending and take top 5
                    trendsData.sort((a, b) => b.qatarMentionCount - a.qatarMentionCount);
                    setCountryTrends(trendsData.slice(0, 5));
                    setCountryTrendsLoading(false);
                    console.log('Country trends fetched for all platforms:', trendsData);
                }, (error) => {
                    console.error('Error fetching country trends:', error);
                    setCountryTrends([]);
                    setCountryTrendsLoading(false);
                });
                
                // Clean up the listener when the component unmounts
                return () => unsubscribeCountryTrends();
            } catch (err) {
                console.error('Error setting up country trends listener:', err);
                setCountryTrends([]);
                setCountryTrendsLoading(false);
            }
        };
        fetchCountryTrends();

        return () => {
            unsubscribeDailyFlow();
            unsubscribeCategoryDist();
            // Note: The countryTrends listener cleanup is handled in the fetchCountryTrends function
        };
    }, [theme, isRTL, t]);

    return (
        <View style={{ flex: 1, backgroundColor: colors.background }}>
            <StatusBar barStyle={theme === 'dark' ? 'light-content' : 'dark-content'} />
            
            {/* Header with Gradient */}
            <LinearGradient
                colors={theme === 'dark' 
                    ? ['#8A1538', '#6B0F2C', '#121212'] 
                    : ['#8A1538', '#A61E42', '#CC2952']}
                style={styles.headerGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
            >
                <Animated.View entering={FadeInDown.delay(200).duration(600)} style={styles.header}>
                    <Text style={[styles.title, { color: '#FFFFFF', textAlign: isRTL ? 'right' : 'left', fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif' }]}>
                        {t('dashboard.title')}
                    </Text>
                    <Text style={[styles.subtitle, { color: '#FFFFFF', textAlign: isRTL ? 'right' : 'left', fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'sans-serif' }]}>
                        {t('dashboard.insights')}
                    </Text>
                </Animated.View>
            </LinearGradient>
            
            <ScrollView style={styles.container}>
                <Animated.View 
                    entering={FadeInUp.delay(300).duration(600)}
                    style={[styles.chartContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}
                >
                  <View style={styles.cardHeader}>
                    <Text style={[styles.chartTitle, { color: colors.text, textAlign: isRTL ? 'right' : 'left', fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif' }]}>
                        Countries Mentioned
                    </Text>
                    <Ionicons name="analytics-outline" size={20} color={colors.primary} />
                  </View>
                  <View style={{ marginTop: 20 }}>
                    {countryTrendsLoading ? (
                      <ActivityIndicator size="large" color={colors.primary} style={{ marginVertical: 20 }}/>
                    ) : countryTrends.length > 0 ? (
                      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 16 }}>
                        <BarChart
                          data={{
                            labels: countryTrends.map(c => c.countryName),
                            datasets: [{ data: countryTrends.map(c => c.qatarMentionCount) }],
                          }}
                          width={screenWidth - 70}
                          height={220}
                          yAxisLabel=""
                          yAxisSuffix=""
                          chartConfig={{
                            backgroundGradientFrom: colors.surface,
                            backgroundGradientTo: colors.surface,
                            color: (opacity = 1) => colors.primary,
                            labelColor: (opacity = 1) => colors.textSecondary,
                            barPercentage: 0.6,
                          }}
                          style={{ borderRadius: 8 }}
                          fromZero
                          showValuesOnTopOfBars
                        />
                      </View>
                    ) : (
                      <Text style={{ color: colors.textSecondary, textAlign: 'center', marginTop: 20, fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'sans-serif' }}>
                        No country data available.
                      </Text>
                    )}
                  </View>
                </Animated.View>

                <Animated.View 
                    entering={FadeInUp.delay(400).duration(600)}
                    style={[styles.chartContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}
                >
                    <View style={styles.cardHeader}>
                        <Text style={[styles.chartTitle, { color: colors.text, textAlign: isRTL ? 'right' : 'left', fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif' }]}>
                            {t('dashboard.trendPrediction')}
                        </Text>
                        <Ionicons name="trending-up-outline" size={20} color={colors.primary} />
                    </View>
                    <TrendChart />
                </Animated.View>

                {loading ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color={colors.primary} />
                        <Text style={{ color: colors.textSecondary, marginTop: 10, fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'sans-serif' }}>
                            {t('dashboard.loading')}
                        </Text>
                    </View>
                ) : (
                    <>
                        <Animated.View 
                            entering={FadeInUp.delay(500).duration(600)}
                            style={[styles.chartContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}
                        >
                            <View style={styles.cardHeader}>
                                <Text style={[styles.chartTitle, { color: colors.text, textAlign: isRTL ? 'right' : 'left', fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif' }]}>
                                    {t('dashboard.newsVolume')}
                                </Text>
                                <Ionicons name="stats-chart-outline" size={20} color={colors.primary} />
                            </View>
                            <LineChart 
                                data={dailyFlowData} 
                                width={screenWidth - 40} 
                                height={220} 
                                chartConfig={chartConfig} 
                                bezier 
                                style={styles.chart} 
                            />
                        </Animated.View>
                        <Animated.View 
                            entering={FadeInUp.delay(600).duration(600)}
                            style={[styles.chartContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}
                        >
                            <View style={styles.cardHeader}>
                                <Text style={[styles.chartTitle, { color: colors.text, textAlign: isRTL ? 'right' : 'left', fontFamily: Platform.OS === 'ios' ? 'Georgia' : 'serif' }]}>
                                    {t('dashboard.categoryDistribution')}
                                </Text>
                                <Ionicons name="pie-chart-outline" size={20} color={colors.primary} />
                            </View>
                            <PieChart 
                                data={categoryData} 
                                width={screenWidth - 40} 
                                height={220} 
                                chartConfig={chartConfig} 
                                accessor={"population"} 
                                backgroundColor={"transparent"} 
                                paddingLeft={"15"} 
                                center={[10, 0]} 
                                absolute 
                                style={styles.chart} 
                            />
                        </Animated.View>
                    </>
                )}
                
                {/* Add Floating Action Button */}
                <TouchableOpacity 
                    style={[styles.fab, { backgroundColor: colors.primary }]}
                    activeOpacity={0.8}
                >
                    <Ionicons name="add" size={24} color="#FFFFFF" />
                </TouchableOpacity>
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: { 
        flex: 1,
    },
    headerGradient: {
        borderBottomLeftRadius: 16,
        borderBottomRightRadius: 16,
        ...(Platform.OS === 'web'
          ? { boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }
          : {
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.15,
              shadowRadius: 12,
              elevation: 6,
            }),
    },
    header: { 
        padding: 20, 
        paddingBottom: 10 
    },
    title: {
        fontSize: 32,
        fontWeight: 'bold',
        marginBottom: 8,
        ...(Platform.OS === 'web'
          ? { textShadow: '1px 1px 3px rgba(0, 0, 0, 0.3)' }
          : {
              textShadowColor: 'rgba(0, 0, 0, 0.3)',
              textShadowOffset: { width: 1, height: 1 },
              textShadowRadius: 3,
            }),
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 8,
        ...(Platform.OS === 'web'
          ? { textShadow: '0.5px 0.5px 1px rgba(0, 0, 0, 0.2)' }
          : {
              textShadowColor: 'rgba(0, 0, 0, 0.2)',
              textShadowOffset: { width: 0.5, height: 0.5 },
              textShadowRadius: 1,
            }),
    },
    chartContainer: {
        marginTop: 16,
        marginHorizontal: 16,
        borderRadius: 8,
        paddingVertical: 16,
        marginBottom: 16,
        borderWidth: 1,
        overflow: 'hidden',
        ...(Platform.OS === 'web'
          ? { boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }
          : {
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 3,
            }),
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
        paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(0, 0, 0, 0.05)',
        paddingBottom: 12,
    },
    chartTitle: {
        fontSize: 18,
        fontWeight: '600',
    },
    chart: { 
        borderRadius: 8,
        marginTop: 8,
    },
    loaderContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: 50,
    },
    mapContainer: {
        height: 250,
        borderRadius: 8,
        overflow: 'hidden',
        marginHorizontal: 16,
    },
    map: {
        ...StyleSheet.absoluteFillObject,
    },
    mapPlaceholder: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    fab: {
        position: 'absolute',
        bottom: 20,
        right: 20,
        width: 56,
        height: 56,
        borderRadius: 28,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 6,
        zIndex: 100,
        ...(Platform.OS === 'web'
          ? { boxShadow: '0 4px 8px rgba(0,0,0,0.3)' }
          : {
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 4,
            }),
    },
});

const mapStyleDark = [
  { elementType: "geometry", stylers: [{ color: "#1E1E1E" }] },
  { elementType: "labels.text.fill", stylers: [{ color: "#888888" }] },
  { elementType: "labels.text.stroke", stylers: [{ color: "#121212" }] },
  { featureType: "administrative", elementType: "geometry.stroke", stylers: [{ color: "#444444" }] },
  { featureType: "administrative.land_parcel", stylers: [{ visibility: "off" }] },
  { featureType: "administrative.land_parcel", elementType: "labels.text.fill", stylers: [{ color: "#444444" }] },
  { featureType: "administrative.neighborhood", stylers: [{ visibility: "off" }] },
  { featureType: "landscape", elementType: "geometry", stylers: [{ color: "#1E1E1E" }] },
  { featureType: "poi", elementType: "geometry", stylers: [{ color: "#2A2A2A" }] },
  { featureType: "poi", elementType: "labels.text", stylers: [{ visibility: "off" }] },
  { featureType: "poi.park", elementType: "geometry", stylers: [{ color: "#2A2A2A" }] },
  { featureType: "poi.park", elementType: "labels.text.fill", stylers: [{ color: "#888888" }] },
  { featureType: "road", elementType: "geometry", stylers: [{ color: "#2A2A2A" }] },
  { featureType: "road", elementType: "labels", stylers: [{ visibility: "off" }] },
  { featureType: "road.arterial", elementType: "geometry", stylers: [{ color: "#373737" }] },
  { featureType: "road.highway", elementType: "geometry", stylers: [{ color: "#444444" }] },
  { featureType: "road.highway", elementType: "geometry.stroke", stylers: [{ color: "#1E1E1E" }] },
  { featureType: "transit", elementType: "geometry", stylers: [{ color: "#2A2A2A" }] },
  { featureType: "water", elementType: "geometry", stylers: [{ color: "#121212" }] },
];