{"name": "qna-news-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@firebase/auth-compat": "^0.6.0", "@lottiefiles/dotlottie-react": "^0.14.3", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/auth": "^22.4.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tiptap/react": "^3.0.1", "@tiptap/starter-kit": "^3.0.1", "axios": "^1.10.0", "expo": "^53.0.20", "expo-blur": "~14.1.5", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linear-gradient": "~14.1.0", "expo-linking": "~7.1.7", "expo-localization": "^16.1.6", "expo-router": "~5.1.4", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "firebase": "^11.10.0", "firebase-admin": "^13.4.0", "lottie-react": "^2.4.1", "lottie-react-native": "^7.2.2", "next": "^15.4.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "^5.1.3"}, "private": true}