import type { DocumentTitleOptions, NavigationContainerRef, ParamListBase } from '@react-navigation/native';
import * as React from 'react';
/**
 * Set the document title for the active screen
 */
export declare function useDocumentTitle(ref: React.RefObject<NavigationContainerRef<ParamListBase> | null>, { enabled, formatter, }?: DocumentTitleOptions): void;
//# sourceMappingURL=useDocumentTitle.d.ts.map