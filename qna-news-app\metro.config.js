const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add support for additional file extensions
config.resolver.assetExts.push(
  // Adds support for `.db` files for SQLite databases
  'db'
);

// Improve source map generation and fix anonymous file issues
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
  },
};

// Fix source map paths to prevent anonymous file errors
config.serializer.createModuleIdFactory = function () {
  return function (modulePath) {
    // Ensure we have a valid path and normalize it
    if (!modulePath || typeof modulePath !== 'string') {
      return modulePath;
    }

    // Convert to relative path and normalize separators
    const relativePath = path.relative(__dirname, modulePath);
    return relativePath.replace(/\\/g, '/');
  };
};

// Improve resolver configuration
config.resolver.platforms = ['native', 'web', 'ios', 'android'];

module.exports = config;
